<template>
  <Teleport to="#new-book-create">
    <q-btn-dropdown
      :class="isTopNavDefault ? '' : 'q-ml-xs'"
      :dense="isTopNavDefault"
      :stretch="isTopNavDefault"
      :color="!isTopNavDefault ? 'primary' : ''"
      :flat="isTopNavDefault"
      :rounded="!isTopNavDefault"
      :padding="!isTopNavDefault ? '13px' : ''"
      :loading="addingBook"
      icon="add"
      :disable="!isAuthor && !isAdmin && !canAddBook"
      label="New"
      no-wrap
    >
      <q-list>
        <q-item
          clickable
          v-close-popup
          @click="
            selectedBookCategory = 'non-fiction';
            newBookHelpPrompt = true;
          "
        >
          <q-item-section avatar>
            <q-icon name="collections_bookmark" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Non-Fiction</q-item-label>
          </q-item-section>
        </q-item>

        <q-item
          clickable
          v-close-popup
          @click="
            selectedBookCategory = 'autobiography';
            newBookHelpPrompt = true;
          "
        >
          <q-item-section avatar>
            <q-icon name="local_library" />
          </q-item-section>
          <q-item-section style="width: 120px">
            <q-item-label>Autobiography</q-item-label>
          </q-item-section>
        </q-item>

        <q-item
          clickable
          v-close-popup
          @click="
            selectedBookCategory = 'faithstory';
            newBookHelpPrompt = true;
          "
        >
          <q-item-section avatar>
            <q-icon name="diversity_1" />
          </q-item-section>
          <q-item-section style="width: 120px">
            <q-item-label>Faith Story</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-btn-dropdown>
  </Teleport>

  <div id="books" class="container q-py-lg q-mx-auto">
    <div id="title" class="row justify-between q-px-lg">
      <h2 class="q-mb-none">Welcome to Manuscriptr, let's write!</h2>
    </div>
    <div class="books-container">
      <div
        v-if="!isAdmin && !isAuthor"
        class="row items-stretch q-px-lg q-pt-md"
      >
        <div class="q-gutter-sm">
          <UnsubscribedNotice
            message="To access Manuscriptr, you need to have an active subscription. Follow the subscribe option to continue!"
          />
        </div>
      </div>

      <div v-if="isReviewer && usersForReview.length">
        <div class="row items-stretch justify-between q-pa-lg q-pt-none">
          <div class="col-xs-12 col-md-7">
            <div v-if="isReviewer" class="q-gutter-sm">
              <ReviewerNotice
                message="You have been assigned the role of Reviewer. Please choose an author whose books you would like to review."
              />
            </div>
          </div>
          <div>
            <q-select
              label="Books"
              v-model="booksView"
              :options="viewOptions"
              style="width: 100px"
            />
          </div>
          <div class="inline">
            <q-select
              label="Select Author"
              v-model="toReview"
              :options="authorsOptions"
              style="width: 250px"
              emit-value
              map-options
            />
          </div>
        </div>
        <div
          class="row items-stretch"
          v-if="['Reviews', 'All'].includes(booksView)"
        >
          <section
            v-for="bookAuthor in booksByAuthor"
            class="col-xs-12 col-md-4 q-pa-lg"
          >
            <BookCard
              :book="bookAuthor"
              @edit="setEditBook(bookAuthor)"
              @delete="deleteSelectedBook(bookAuthor)"
            />
          </section>
        </div>

        <q-separator />
      </div>
      <div v-if="['My Books', 'All'].includes(booksView)">
        <div class="row items-stretch">
          <section
            v-for="book in books"
            :key="book.id"
            class="col-xs-12 col-md-4 q-pa-lg"
          >
            <BookCard
              :book="book"
              @edit="setEditBook(book)"
              @delete="deleteSelectedBook(book)"
            />
          </section>
        </div>
      </div>
    </div>

    <q-inner-loading
      :showing="showing"
      color="primary"
      label="Hang tight, Loading...."
      label-class="text-primary"
      label-style="font-size: 1.1em"
      style="z-index: 1"
    >
      <div class="fixed-center">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <q-spinner-dots color="primary" size="2em" />
        <div class="align-center">
          <q-icon name="img:robot.png" class="q-ml-xs" size="xl" />
          Hang tight, Loading....
        </div>
      </div>
    </q-inner-loading>
  </div>

  <q-dialog v-model="newBookHelpPrompt" persistent>
    <q-card>
      <q-card-section class="q-pa-none">
        <q-bar class="bg-primary text-white">
          <q-icon name="img:robot.png" />

          <div>Create a new book!</div>

          <q-space />

          <q-btn
            dense
            flat
            icon="close"
            @click="newBookHelpPrompt = false"
            v-if="!addingBook"
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
      </q-card-section>
      <q-card-section>
        <p class="text-h4 text-primary">
          Welcome to Manuscriptr!
          <q-img src="robot.png" class="q-ma-sm float-right" width="100px" />
        </p>
        <p class="text-body1">
          {{ newBookDescription }}
        </p>
      </q-card-section>
      <q-separator />
      <q-card-actions>
        <q-btn
          flat
          :disable="addingBook"
          @click="
            addBook({});
            newBookHelpPrompt = false;
          "
        >
          Skip Manny!
        </q-btn>
        <q-btn color="primary" :loading="addingBook" @click="setEmptyBook()">
          Use Manny!
        </q-btn>
      </q-card-actions>
    </q-card>
  </q-dialog>

  <NewBookDialog
    v-if="selectedBookCategory === 'non-fiction'"
    v-model:initial-values="initialValues"
    v-model:initial-outlines="initialOutlines"
    :modal="onboardingActive"
    @completed="addBook"
    @updated="updateBookNow"
    @cancel="onboardingActive = false"
  />
  <NewFaithStoryFlow
    v-if="selectedBookCategory === 'faithstory'"
    v-model:initial-values="initialValues"
    v-model:initial-outlines="initialOutlines"
    @completed="addBook"
    :modal="onboardingActive"
    @updated="updateBookNow"
    @cancel="onboardingActive = false"
  />
  <NewAutoBioFlow
    v-if="selectedBookCategory === 'autobiography'"
    v-model:initial-values="initialValues"
    v-model:initial-outlines="initialOutlines"
    @completed="addBook"
    :modal="onboardingActive"
    @updated="updateBookNow"
    @cancel="onboardingActive = false"
  />
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted, toRef } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import NewBookDialog from 'src/entities/book/ui/components/onboarding/nonfiction/NewBookFlow.vue';
import UnsubscribedNotice from 'src/entities/package/ui/UnsubscribedNotice.vue';
import {
  confirmDeletion,
  confirmOverrideText,
} from 'src/shared/lib/quasar-dialogs';

import {
  isAdmin,
  isAuthor,
  isReviewer,
  isReviewMode,
  loginAs,
  useAuthorStore,
  user,
} from 'src/entities/user';

import {
  type Book,
  BookCard,
  createBook,
  createEmptyBook,
  listBooks,
  deleteBook,
  updateBook,
  listOutlines,
  deleteBookById,
  setNewBook,
  ChapterOutline,
} from 'src/entities/book';
import ReviewerNotice from 'src/entities/package/ui/ReviewerNotice.vue';
import { computedAsync } from '@vueuse/core/index';
import { ACTIONS, loggingService, PAGES } from 'src/entities/log';
import { isTopNavDefault } from 'src/entities/setting';
import {
  NewFaithStoryFlow,
  NewAutoBioFlow,
} from 'src/entities/book/ui/components/onboarding';

// Local state variables
const addingBook = ref(false);
const onboardingActive = ref(false);
const newBookHelpPrompt = ref(false);
const initialValues = ref<Book | undefined>(undefined);
const initialOutlines = ref<ChapterOutline[] | undefined>(undefined);
const authorStore = useAuthorStore();
const selectedBookCategory = ref('non-fiction');

// Fetching the list of books for the current user
const userGetBooks = computed(() => loginAs.value || user.value!.uid);
const bookList = ref(await listBooks(userGetBooks.value));
// Create a computed property to filter books
const books = computed(() => {
  return bookList.value.filter((book) => !book?.deleted);
});

// Router instance for navigation
const router = useRouter();

// Quasar instance for notification and dialog
const $q = useQuasar();

// Get the adding of new book description
const newBookDescription = computed(() => {
  switch (selectedBookCategory.value) {
    case 'autobiography':
      return 'Use Manny, "our AI" to write your Autobiography Book\'s  title, subtitle, and chapter outline or start writing!';
    case 'faithstory':
      return 'Use Manny, "our AI" to write your Faith Story Book\'s  title, subtitle, and chapter outline or start writing!';
    default:
      return `Use Manny, "our AI" to write your Non-Fiction Book's mission statement, title, subtitle,
          and chapter outline or start writing!`;
  }
});

// Fetching the list of books for the current user
const userId = user.value!.uid; // Get the user ID from the current user's value
const isInReview = computed(() => isReviewMode.value.length > 0); // Check if the review mode is active
const allowed_no_of_books = computedAsync(async () => {
  if (user.value?.uid) {
    const author = await authorStore.getAuthorWithoutSync(
      user.value?.uid as string,
    ); // Get the author's data using the user ID
    return author.allowed_books; // Return the number of allowed books for the author
  }
}, 4); // Default value is 4

// List of authors available for review
const usersForReview = isAdmin.value
  ? authorStore.listAllAuthorsForReview() // List all authors if the user is an admin
  : authorStore.listAuthorsForReview(user.value!.uid); // List authors for review based on the current user's ID

const toReview = ref(''); // Selected author for review
let booksByAuthor = ref([]); // List of books by the selected author
const showing = ref(false); // Indicates if the loading indicator should be shown
const booksView = ref(isReviewer.value ? 'Reviews' : 'My Books'); // Default view based on the user role
const viewOptions = ['All', 'My Books', 'Reviews']; // View options for the books
const bookFoundationAutosave = ref(true);

// Options for selecting authors in the dropdown
const authorsOptions = computed(() => {
  const userOptions = usersForReview.value.map((user) => ({
    label: user.name || user.email, // Use the user's name or email as the label
    value: user.id, // Use the user's ID as the value
  }));
  if (!toReview.value && userOptions.length) {
    toReview.value = userOptions[0].value as string; // Default to the second user if no review is selected
    booksByAuthor = toReview.value ? listBooks(toReview.value) : ref([]); // Fetch the books for the selected author
  }
  return userOptions; // Return the default option followed by the user options
});

if (isReviewMode.value) {
  toReview.value = isReviewMode.value; // Set the review mode value to the selected review
  booksByAuthor = toReview.value ? listBooks(toReview.value) : ref([]); // Fetch the books for the selected author
}

// Watch for changes in the selected author for review
watch(toReview, (authorSelected) => {
  showing.value = true; // Show the loading indicator
  setTimeout(() => {
    booksByAuthor = authorSelected ? listBooks(authorSelected) : ref([]); // Fetch the books for the selected author
    showing.value = false; // Hide the loading indicator
  }, 500);

  if (authorSelected) {
    isReviewMode.value = toReview.value; // Set the review mode value
  } else {
    isReviewMode.value = ''; // Clear the review mode value
  }
});

// Watch for changes in the view of the books
watch(booksView, (bView) => {
  showing.value = true; // Show the loading indicator
  setTimeout(() => {
    showing.value = false; // Hide the loading indicator
  }, 500);
});

// Define the module or component name
const MODULE_NAME = 'book';

/**
 * Function to check if the user can add more books
 */
const canAddBook = () => {
  return !(
    isAdmin.value ||
    (isAuthor.value && books.value.length < allowed_no_of_books.value)
  );
};

/**
 * Function to set empty book once book foundation is clicked
 */
const setEmptyBook = async () => {
  bookFoundationAutosave.value = true;
  addingBook.value = true;
  const newBook = await setNewBook(selectedBookCategory.value);
  initialValues.value = newBook;
  newBookHelpPrompt.value = false;
  initialOutlines.value = [];
  onboardingActive.value = true;
  addingBook.value = false;
};
const setEditBook = async (book: Book) => {
  selectedBookCategory.value = book.category;
  bookFoundationAutosave.value = true;
  initialValues.value = book;
  const editBookOutlines = await getBookOutlines(book.id);
  initialOutlines.value = editBookOutlines ? editBookOutlines : [];

  onboardingActive.value = true;
};

/**
 * Function to get outlines for a specific book
 * @param bookId - The ID of the book to get outlines for
 */
const getBookOutlines = async (bookId: string) => {
  const outlines = await listOutlines(bookId);
  return outlines.value.outlines;
};

/**
 * Function to add a new book
 * @param prefilled - Optional prefilled data for the book
 */
const addBook = async (prefilled = {}) => {
  addingBook.value = true;
  onboardingActive.value = false;

  try {
    const book = await createBook({
      ...createEmptyBook(selectedBookCategory.value),
      authorId: user.value!.uid,
      ...prefilled,
    });
    await loggingService.logAction(
      PAGES.BOOKLIST,
      ACTIONS.CREATE,
      `Created a new book: ${book.title || 'Untitled book'}`,
      MODULE_NAME,
      book.id,
    );
    if (book?.id) await router.push(`/books/${book.id}`);
  } finally {
    addingBook.value = false;
  }
};

/**
 * Function to update an existing book
 * @param id - The ID of the book to update
 * @param newData - The new data to update the book with
 */
const updateBookConfirm = async (id: string, newData: Partial<Book>) => {
  if (initialValues.value && initialValues.value.id) {
    const shouldSave = await confirmOverrideText($q, {
      message: `Are you sure you want to save the changes?`,
    });
    if (!shouldSave) {
      return;
    }
    await updateBookNow(id, newData);
  }
};
const updateBookNow = async (id: string, newData: Partial<Book>) => {
  await updateBook(id, newData);
  initialValues.value = undefined;
  initialOutlines.value = [];
  onboardingActive.value = false;
  $q.notify('Book updated successfully!');
};

/**
 * Function to delete a book
 * @param book - The book to delete
 */
const deleteSelectedBook = (book: Book) => {
  showing.value = true;
  confirmDeletion($q)
    .then(() => {
      deleteBookById(book.id);
    })
    .then(async () => {
      console.log('Delete Book: ', book.id);
      deleteBook(book.id);
      await loggingService.logAction(
        PAGES.BOOKLIST,
        ACTIONS.DELETE,
        `Book is deleted: ${book.title || 'Untitled book'}`,
        MODULE_NAME,
        book.id,
      );
    })
    .catch(() => {})
    .finally(() => (showing.value = false));
};
</script>

<style lang="scss">
#books .q-item__section--main ~ .q-item__section--side {
  display: none;
}
#books .book-heading {
  display: flex;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: center;

  .write-button {
    margin-left: 16px;
  }
}
</style>
