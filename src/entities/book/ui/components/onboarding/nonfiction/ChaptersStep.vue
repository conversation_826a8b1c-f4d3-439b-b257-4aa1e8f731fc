<template>
  <OnboardingStepWrapper
    theme="default"
    content-title="Chapter Outline"
    content-subtitle="Create your book's structure with compelling chapter titles that guide readers through your content."
    content-icon="list_alt"
    :show-actions="false"
  >
    <OnboardingFormSection
      label="Book Chapters"
      description="Organize your content into logical chapters. Each chapter should cover a specific topic or concept."
      icon="menu_book"
    >
      <div class="form-actions">
        <q-btn
          class="action-btn btn-secondary"
          :disable="loadingChapters"
          @click="addNewChapter"
          icon="add"
          label="Add Chapter"
          outline
        >
          <q-tooltip>Add a new chapter</q-tooltip>
        </q-btn>

        <MannyButtonLarge
          label="Ask Manny for Suggestions"
          :loading="loadingChapters"
          :disabled="loadingChapters"
          tooltip="Let Manny generate chapter suggestions"
          @click="generateChapters(false)"
        />

        <q-btn
          @click="$emit('complete')"
          class="action-btn btn-primary"
          label="Save Progress"
          icon="save"
          v-if="!isScreenBiggerMd"
        >
          <q-tooltip>Save Book Foundations</q-tooltip>
        </q-btn>
      </div>
    </OnboardingFormSection>

    <OnboardingChapterList
      v-model="chapters"
      :loading="loadingChapters"
      :allow-reorder="true"
      empty-state-text="Add your first chapter"
      loading-text="Manny is generating chapter suggestions..."
      @add-chapter="addNewChapter"
      @delete-chapter="deleteChapter"
    />
  </OnboardingStepWrapper>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import {
  confirmOverrideText,
  showError,
  warn,
} from 'src/shared/lib/quasar-dialogs';
import { composeText } from 'src/shared/api/openai';
import type { NewChapterOutline } from 'src/entities/book';
import {
  OnboardingStepWrapper,
  OnboardingFormSection,
  OnboardingChapterList,
  MannyButtonLarge,
} from '../shared';

const props = defineProps<{
  modelValue: NewChapterOutline[];
  title: string;
  subtitle: string;
  isScreenBiggerMd?: boolean;
}>();
const emit = defineEmits<{
  'update:modelValue': [value: string[]];
  complete: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);

const $q = useQuasar();

const loadingChapters = ref(false);

const addNewChapter = () => {
  chapters.value.push({
    title: '',
    outline: '',
    wordCount: 0,
    number: chapters.value.length,
    isSection: false,
  });
};

const deleteChapter = async (index: number) => {
  const shouldDelete = await confirmOverrideText($q, {
    message: `Are you sure you want to delete this chapter?`,
  });
  if (!shouldDelete) {
    return;
  }
  chapters.value.splice(index, 1);
};
const prompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines using the following criteria:
  1. provide a numbered list. For example: "1. Unveiling Prosperity's Blueprint"
  2. each chapter title should be within 4-6 words and be based on the promises and benefits identified in the title and subtitle. They should follow a structure flow/theme of getting started to mastery, and not repeated.
  3. titles should be based on the promises and benefits of the book identified in the title and subtitle
  4. using clever, compelling, eye catching language
  5. Please avoid any Markdown or HTML formatting in your response

  The provided information is as follows:
  Book title: ${props.title}
  Book subtitle: ${props.subtitle}`;
});

const results = ref('');

onMounted(async () => {
  if (chapters.value) {
    try {
      await generateChapters(true);
    } catch (e) {
      console.error(e);
    }
  }
});
async function generateChapters(invoking: boolean = false) {
  const { title, subtitle } = props;
  if ((!title || !subtitle) && !invoking) {
    warn($q, {
      title: 'Missing information',
      message: 'You must first fill the title, and subtitle',
    });
    return;
  }
  let promptRequest: string = prompt.value;
  if (chapters.value.length && !invoking) {
    const shouldDelete = await confirmOverrideText($q, {
      message:
        'This action will delete all chapters and their content that you have written. Are you sure you want to proceed?',
    });

    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (!invoking) loadingChapters.value = true;
  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value}`;
  }

  try {
    const response = await composeText(promptRequest);

    const newChapters: NewChapterOutline[] = response
      .trim()
      .split('\n')
      .filter((line) => /^\d+\./.test(line))
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0)
      .map((chapter, idx) => ({
        title: chapter,
        wordCount: 0,
        isSection: false,
        number: idx + 1,
        outline: '',
      }));
    if (!invoking) chapters.value = newChapters;

    // save the results
    results.value = `${results.value} ${response}`;
  } catch (e) {
    console.error(e);
    showError($q, (e as Error).message);
  } finally {
    loadingChapters.value = false;
  }
}
</script>
