<template>
  <OnboardingStepWrapper
    theme="autobiography"
    content-title="Chapter Outline"
    content-subtitle="Create your book's structure with compelling chapter titles that guide readers through your life journey."
    content-icon="list_alt"
    :show-actions="false"
  >
    <OnboardingFormSection
      label="Book Chapters"
      description="Organize your life story into logical chapters. Each chapter should cover a specific period, theme, or life event."
      icon="menu_book"
    >
      <div class="form-actions">
        <q-btn
          class="action-btn btn-secondary"
          :disable="loadingChapters"
          @click="addNewChapter"
          icon="add"
          label="Add Chapter"
          outline
        >
          <q-tooltip>Add a new chapter</q-tooltip>
        </q-btn>

        <MannyButtonLarge
          label="Ask Manny for Suggestions"
          :loading="loadingChapters"
          :disabled="loadingChapters"
          tooltip="Let <PERSON> generate chapter suggestions"
          @click="generateChapters(false)"
        />

        <q-btn
          @click="$emit('complete')"
          class="action-btn btn-primary"
          label="Save Progress"
          icon="save"
          v-if="!isScreenBiggerMd"
        >
          <q-tooltip>Save Book Foundations</q-tooltip>
        </q-btn>
      </div>
    </OnboardingFormSection>

    <OnboardingChapterList
      v-model="chapters"
      :loading="loadingChapters"
      :allow-reorder="true"
      empty-state-text="Add your first chapter"
      loading-text="Manny is generating chapter suggestions..."
      @add-chapter="addNewChapter"
      @delete-chapter="deleteChapter"
    />
  </OnboardingStepWrapper>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { useQuasar } from 'quasar';
import {
  confirmOverrideText,
  showError,
  warn,
} from 'src/shared/lib/quasar-dialogs';
import { composeText } from 'src/shared/api/openai';
import type { NewChapterOutline } from 'src/entities/book';
import { AutoBioGraphyMissionQuestions } from 'src/entities/book/model/types';
import {
  OnboardingStepWrapper,
  OnboardingFormSection,
  OnboardingChapterList,
  MannyButtonLarge,
} from '../shared';

const props = defineProps<{
  modelValue: NewChapterOutline[];
  title: string;
  subtitle: string;
  questions?: AutoBioGraphyMissionQuestions;
  isScreenBiggerMd?: boolean;
}>();
const emit = defineEmits<{
  'update:modelValue': [value: string[]];
  complete: [];
}>();

const chapters = useVModel(props, 'modelValue', emit);
const questions = useVModel(props, 'questions', emit);

const $q = useQuasar();

const loadingChapters = ref(false);

const addNewChapter = () => {
  chapters.value.push({
    title: '',
    outline: '',
    wordCount: 0,
    number: chapters.value.length,
    isSection: false,
  });
};

const deleteChapter = async (index: number) => {
  const shouldDelete = await confirmOverrideText($q, {
    message: `Are you sure you want to delete this chapter?`,
  });
  if (!shouldDelete) {
    return;
  }
  chapters.value.splice(index, 1);
};
const prompt = computed(() => {
  return `Using the book title and subtitle provided below, generate 9 chapter title outlines using the following criteria:
  <ol>
    <li>Provide a numbered list. For example: "1. Chasing Shadows in the Rain"</li>
    <li>Each chapter title should be 4-6 words, based on the emotional tone, key events, or personal themes suggested by the title, subtitle, and the author's answers.</li>
    <li>The chapter titles should loosely follow the order the author chose (chronological if they selected life stages, or thematic if they selected lessons/challenges/turning points).</li>
    <li>The chapter titles should reflect the author's stated main reason for sharing their story (legacy, inspiration, education, or transformation).</li>
    <li>Use vivid, compelling, and memorable language that captures emotion, growth, or pivotal moments.</li>
    <li>Avoid using Markdown or HTML formatting in your response.</li>
  </ol>
  <br />
  The provided information is as follows:
  <ul>
    <li>Book title: ${props.title}</li>
    <li>Book subtitle: ${props.subtitle}</li>
    <li>Author's main reason for sharing their story: ${questions.value.mainReason}</li>
    <li>Book type focus: ${questions.value.bookFocused}</li>
    <li>Book order preference: ${questions.value.bookOrder}</li>
    <li>Target audience: ${questions.value.description}</li>
    <li>Target audience age: ${questions.value.age}</li>
  </ul>`;
});

const results = ref('');

onMounted(async () => {
  if (chapters.value) {
    try {
      await generateChapters(true);
    } catch (e) {
      console.error(e);
    }
  }
});
async function generateChapters(invoking: boolean = false) {
  const { title, subtitle } = props;
  if ((!title || !subtitle) && !invoking) {
    warn($q, {
      title: 'Missing information',
      message: 'You must first fill the title, and subtitle',
    });
    return;
  }
  let promptRequest: string = prompt.value;
  if (chapters.value.length && !invoking) {
    const shouldDelete = await confirmOverrideText($q, {
      message:
        'This action will delete all chapters and their content that you have written. Are you sure you want to proceed?',
    });

    if (!shouldDelete) {
      return;
    }
    results.value =
      typeof results.value === 'string' ? results.value : await results.value;
  }
  if (!invoking) loadingChapters.value = true;

  if (results.value) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value}`;
  }

  try {
    const response = await composeText(promptRequest);

    const newChapters: NewChapterOutline[] = response
      .trim()
      .split('\n')
      .filter((line) => /^\d+\./.test(line))
      .map((line) => line.replace(/^\d+\.\s*/, ''))
      .map((line) => line.replace(/^"|"$/g, ''))
      .filter((line) => line.length > 0)
      .map((chapter, idx) => ({
        title: chapter,
        wordCount: 0,
        isSection: false,
        number: idx + 1,
        outline: '',
      }));
    if (!invoking) chapters.value = newChapters;

    // save the results
    results.value = `${results.value} ${newChapters}`;
  } catch (e) {
    console.error(e);
    showError($q, (e as Error).message);
  } finally {
    loadingChapters.value = false;
  }
}
</script>
