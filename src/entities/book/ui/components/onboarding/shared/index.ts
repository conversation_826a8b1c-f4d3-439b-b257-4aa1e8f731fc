// Shared Onboarding Components
export { default as OnboardingHeader } from './OnboardingHeader.vue';
export { default as OnboardingStepIndicator } from './OnboardingStepIndicator.vue';
export { default as OnboardingContentHeader } from './OnboardingContentHeader.vue';
export { default as OnboardingFormSection } from './OnboardingFormSection.vue';
export { default as OnboardingInput } from './OnboardingInput.vue';
export { default as OnboardingSuggestionList } from './OnboardingSuggestionList.vue';
export { default as OnboardingChapterList } from './OnboardingChapterList.vue';
export { default as OnboardingActionButtons } from './OnboardingActionButtons.vue';
export { default as OnboardingOptionGrid } from './OnboardingOptionGrid.vue';
export { default as OnboardingProgressBar } from './OnboardingProgressBar.vue';
export { default as OnboardingLoadingState } from './OnboardingLoadingState.vue';
export { default as <PERSON><PERSON><PERSON><PERSON> } from './MannyButton.vue';
export { default as <PERSON><PERSON><PERSON><PERSON><PERSON>ar<PERSON> } from './MannyButtonLarge.vue';
export { default as OnboardingStepWrapper } from './OnboardingStepWrapper.vue';

// Component types for better TypeScript support
export interface OnboardingStep {
  id: string;
  label: string;
}

export interface OnboardingOption {
  label: string;
  value: string;
  description?: string;
  icon?: string;
}

export interface OnboardingSuggestion {
  label: string;
  value: string;
  description?: string;
}

export interface OnboardingSuggestionColumn {
  key: string;
  title: string;
  icon?: string;
  suggestions: OnboardingSuggestion[];
}
