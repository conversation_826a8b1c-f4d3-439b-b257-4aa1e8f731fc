<template>
  <div class="suggestions-section">
    <!-- Loading State -->
    <div v-if="loading" class="suggestions-loading">
      <div class="loading-skeleton">
        <div v-for="i in gridColumns" :key="i" class="skeleton-column">
          <div class="skeleton-header"></div>
          <div v-for="j in 3" :key="j" class="skeleton-item"></div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!hasValidColumns" class="suggestions-empty">
      <div class="empty-icon">
        <q-icon name="lightbulb_outline" />
      </div>
      <div class="empty-title">{{ emptyTitle }}</div>
      <div class="empty-description">{{ emptyDescription }}</div>
    </div>

    <!-- Suggestions Grid -->
    <div v-else class="suggestions-grid" :class="gridClass">
      <div
        v-for="column in validColumns"
        :key="column.key"
        class="suggestion-column"
        :class="{ 'has-selections': hasSelections(column.key) }"
      >
        <div class="column-header">
          <div class="column-icon" v-if="column.icon">
            <q-icon :name="column.icon" />
          </div>
          <div class="column-title">
            {{ column.title }}
            <span v-if="showCounts" class="suggestion-count">
              ({{ column.suggestions.length }})
            </span>
          </div>
        </div>

        <div class="suggestion-list" :class="{ compact: compact }">
          <div
            v-for="(suggestion, index) in column.suggestions"
            :key="`${column.key}-${index}`"
            class="suggestion-item onboarding-fade-in"
            :class="{
              selected: isSelected(column.key, suggestion.value),
              'has-description': suggestion.description,
            }"
            :tabindex="0"
            role="button"
            :aria-pressed="isSelected(column.key, suggestion.value)"
            :aria-label="`Select ${suggestion.label}`"
            @click="selectSuggestion(column.key, suggestion.value)"
            @keydown.enter="selectSuggestion(column.key, suggestion.value)"
            @keydown.space.prevent="
              selectSuggestion(column.key, suggestion.value)
            "
          >
            <div class="suggestion-content">
              <div class="suggestion-main">
                <div class="suggestion-text">{{ suggestion.label }}</div>
                <div
                  v-if="suggestion.description"
                  class="suggestion-description"
                >
                  {{ suggestion.description }}
                </div>
              </div>
              <div class="suggestion-actions">
                <q-icon :name="checkIcon" class="check-icon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Suggestion {
  label: string;
  value: string;
  description?: string;
}

interface Column {
  key: string;
  title: string;
  icon?: string;
  suggestions: Suggestion[];
}

interface Props {
  columns?: Column[];
  selectedValues?: Record<string, string>;
  checkIcon?: string;
  gridColumns?: 2 | 3;
  loading?: boolean;
  compact?: boolean;
  showCounts?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
}

const props = withDefaults(defineProps<Props>(), {
  checkIcon: 'check_circle',
  gridColumns: 2,
  loading: false,
  compact: false,
  showCounts: false,
  emptyTitle: 'No suggestions available',
  emptyDescription: 'Generate some suggestions to see them here.',
});

const emit = defineEmits<{
  select: [key: string, value: string];
}>();

// Computed properties
const gridClass = computed(() => {
  return `grid-${props.gridColumns}`;
});

const validColumns = computed(() => {
  return (
    props.columns?.filter((column) => column.suggestions?.length > 0) || []
  );
});

const hasValidColumns = computed(() => {
  return validColumns.value.length > 0;
});

const isSelected = (key: string, value: string) => {
  return props.selectedValues?.[key] === value;
};

const hasSelections = (key: string) => {
  return props.selectedValues?.[key] !== undefined;
};

const selectSuggestion = (key: string, value: string) => {
  emit('select', key, value);
};
</script>
