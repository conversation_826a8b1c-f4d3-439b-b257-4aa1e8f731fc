<template>
  <div class="demo-container">
    <h2>OnboardingSuggestionList Demo</h2>
    
    <div class="demo-controls">
      <q-btn @click="toggleLoading" :label="loading ? 'Stop Loading' : 'Show Loading'" />
      <q-btn @click="toggleCompact" :label="compact ? 'Normal View' : 'Compact View'" />
      <q-btn @click="toggleCounts" :label="showCounts ? 'Hide Counts' : 'Show Counts'" />
      <q-btn @click="clearSuggestions" label="Clear Suggestions" />
      <q-btn @click="loadSuggestions" label="Load Suggestions" />
    </div>

    <div class="demo-section">
      <h3>Enhanced Suggestion List</h3>
      <OnboardingSuggestionList
        :columns="suggestionColumns"
        :selected-values="selectedValues"
        :loading="loading"
        :compact="compact"
        :show-counts="showCounts"
        :grid-columns="2"
        empty-title="No AI suggestions yet"
        empty-description="Click 'Load Suggestions' to see AI-generated content recommendations."
        @select="handleSelect"
      />
    </div>

    <div class="demo-section">
      <h3>Selected Values</h3>
      <pre>{{ JSON.stringify(selectedValues, null, 2) }}</pre>
    </div>

    <div class="demo-section">
      <h3>Three Column Layout</h3>
      <OnboardingSuggestionList
        :columns="threeColumnSuggestions"
        :selected-values="selectedValues"
        :grid-columns="3"
        @select="handleSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { OnboardingSuggestionList, type OnboardingSuggestion } from './index';

// Demo state
const loading = ref(false);
const compact = ref(false);
const showCounts = ref(false);
const selectedValues = ref<Record<string, string>>({});
const hasSuggestions = ref(true);

// Sample suggestions with descriptions
const titleSuggestions: OnboardingSuggestion[] = [
  {
    label: "The Entrepreneur's Mindset",
    value: "The Entrepreneur's Mindset",
    description: "A comprehensive guide to thinking like a successful entrepreneur"
  },
  {
    label: "Building Your Business Empire",
    value: "Building Your Business Empire",
    description: "Step-by-step strategies for scaling your business"
  },
  {
    label: "From Idea to IPO",
    value: "From Idea to IPO",
    description: "The complete journey of building a public company"
  },
  {
    label: "Digital Transformation Secrets",
    value: "Digital Transformation Secrets",
    description: "How to modernize your business for the digital age"
  }
];

const subtitleSuggestions: OnboardingSuggestion[] = [
  {
    label: "How to Think, Act, and Succeed Like a CEO",
    value: "How to Think, Act, and Succeed Like a CEO",
    description: "Develop the leadership skills that drive success"
  },
  {
    label: "Proven Strategies for Sustainable Growth",
    value: "Proven Strategies for Sustainable Growth",
    description: "Build a business that lasts and thrives"
  },
  {
    label: "A Step-by-Step Guide to Business Success",
    value: "A Step-by-Step Guide to Business Success",
    description: "Clear, actionable steps for entrepreneurs"
  },
  {
    label: "Lessons from Silicon Valley's Top Founders",
    value: "Lessons from Silicon Valley's Top Founders",
    description: "Learn from the best in the business"
  }
];

const categorySuggestions: OnboardingSuggestion[] = [
  {
    label: "Business & Entrepreneurship",
    value: "business",
    description: "Professional development and business strategy"
  },
  {
    label: "Self-Help & Personal Development",
    value: "self-help",
    description: "Personal growth and improvement"
  },
  {
    label: "Technology & Innovation",
    value: "technology",
    description: "Digital transformation and tech trends"
  }
];

// Computed suggestion columns
const suggestionColumns = computed(() => {
  if (!hasSuggestions.value) return [];
  
  return [
    {
      key: 'titles',
      title: 'Title Suggestions',
      icon: 'title',
      suggestions: titleSuggestions
    },
    {
      key: 'subtitles',
      title: 'Subtitle Suggestions',
      icon: 'subtitles',
      suggestions: subtitleSuggestions
    }
  ];
});

const threeColumnSuggestions = computed(() => [
  {
    key: 'titles',
    title: 'Titles',
    icon: 'title',
    suggestions: titleSuggestions.slice(0, 2)
  },
  {
    key: 'subtitles',
    title: 'Subtitles',
    icon: 'subtitles',
    suggestions: subtitleSuggestions.slice(0, 2)
  },
  {
    key: 'categories',
    title: 'Categories',
    icon: 'category',
    suggestions: categorySuggestions
  }
]);

// Demo methods
const toggleLoading = () => {
  loading.value = !loading.value;
};

const toggleCompact = () => {
  compact.value = !compact.value;
};

const toggleCounts = () => {
  showCounts.value = !showCounts.value;
};

const clearSuggestions = () => {
  hasSuggestions.value = false;
};

const loadSuggestions = () => {
  hasSuggestions.value = true;
};

const handleSelect = (key: string, value: string) => {
  selectedValues.value = {
    ...selectedValues.value,
    [key]: value
  };
};
</script>

<style scoped lang="scss">
.demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.demo-section {
  margin-bottom: 3rem;
  
  h3 {
    margin-bottom: 1rem;
    color: #374151;
    font-weight: 600;
  }
  
  pre {
    background: #f3f4f6;
    padding: 1rem;
    border-radius: 8px;
    font-size: 14px;
    overflow-x: auto;
  }
}
</style>
