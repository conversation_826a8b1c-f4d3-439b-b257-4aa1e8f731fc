<template>
  <q-select
    v-if="type === 'select'"
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :options="options"
    :disable="disabled"
    :loading="loading"
    :multiple="multiple"
    :use-chips="useChips"
    class="onboarding-fade-in full-width"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" :color="iconColor" />
    </template>
    <template v-slot:append v-if="appendIcon || $slots.append">
      <q-icon
        v-if="appendIcon"
        :name="appendIcon"
        :color="iconColor"
        @click="$emit('append-click')"
        class="cursor-pointer"
      />
      <slot name="append" />
    </template>
  </q-select>

  <q-input
    v-else
    v-model="inputValue"
    :outlined="outlined"
    :label="label"
    :placeholder="placeholder"
    :disable="disabled"
    :type="type"
    :rows="rows"
    :size="size"
    :autogrow="type === 'textarea'"
    class="onboarding-fade-in"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:prepend v-if="prependIcon">
      <q-icon :name="prependIcon" :color="iconColor" />
    </template>
    <template v-slot:append v-if="appendIcon || $slots.append">
      <q-icon
        v-if="appendIcon"
        :name="appendIcon"
        :color="iconColor"
        @click="$emit('append-click')"
        class="cursor-pointer"
      />
      <slot name="append" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue: string | string[];
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  outlined?: boolean;
  type?: string;
  rows?: number;
  size?: string;
  prependIcon?: string;
  appendIcon?: string;
  iconColor?: string;
  options?: string[] | any[];
  multiple?: boolean;
  useChips?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  outlined: true,
  type: 'text',
  iconColor: 'primary',
  multiple: false,
  useChips: false,
});

const inputValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const emit = defineEmits<{
  'update:modelValue': [value: string | string[]];
  'append-click': [];
}>();
</script>

<style scoped lang="scss">
// Styles are handled by onboarding-common.scss
</style>
