// Onboarding Components Common Styles
// Inspired by StreamingDialog.vue design patterns

// Import Quasar variables
@import '~quasar/src/css/variables.sass';

// ===== VARIABLES =====
$onboarding-primary: $primary;
$onboarding-secondary: #667eea;
$onboarding-accent: #764ba2;
$onboarding-success: #4ade80;
$onboarding-error: #ef4444;
$onboarding-warning: #f59e0b;
$onboarding-gray-50: #f9fafb;
$onboarding-gray-100: #f3f4f6;
$onboarding-gray-200: #e5e7eb;
$onboarding-gray-300: #d1d5db;
$onboarding-gray-400: #94a3b8;
$onboarding-gray-500: #6b7280;
$onboarding-gray-600: #4b5563;
$onboarding-gray-700: #374151;
$onboarding-gray-800: #1f2937;
$onboarding-gray-900: #111827;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// Border radius
$border-radius-sm: 6px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);

// ===== ONBOARDING CONTAINER =====
.onboarding-container {
  border-radius: $border-radius-xl;
  overflow: hidden;
  box-shadow: $shadow-xl;
  background: linear-gradient(135deg, $onboarding-gray-50 0%, white 100%);
  min-height: 100vh;

  .q-card {
    border-radius: $border-radius-xl;
    box-shadow: $shadow-xl;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: transparent;
  }
}

// ===== ONBOARDING STEP WRAPPER =====
.onboarding-step {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 200px);
  background: linear-gradient(135deg, $onboarding-gray-50 0%, white 100%);

  .step-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: 0 $spacing-lg;

    @media (max-width: 768px) {
      padding: 0 $spacing-md;
    }
  }
}

// ===== ENHANCED HEADER STYLES =====
.onboarding-header {
  background: linear-gradient(
    135deg,
    $onboarding-primary 0%,
    $onboarding-secondary 100%
  );
  color: white;
  padding: $spacing-xl;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: $spacing-lg;
    position: relative;
    z-index: 1;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: $spacing-md;
    }
  }

  .header-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: $shadow-lg;

    @media (max-width: 768px) {
      width: 56px;
      height: 56px;
    }

    .q-icon {
      font-size: 32px;
      color: white;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }
  }

  .header-text {
    flex: 1;

    .title {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 8px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 24px;
      }
    }

    .subtitle {
      font-size: 16px;
      opacity: 0.9;
      line-height: 1.4;

      @media (max-width: 768px) {
        font-size: 14px;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .auto-save-indicator {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      font-size: 14px;
      opacity: 0.9;
      padding: $spacing-sm $spacing-md;
      background: rgba(255, 255, 255, 0.1);
      border-radius: $border-radius-md;
      backdrop-filter: blur(10px);
    }

    .save-btn {
      padding: $spacing-sm $spacing-lg;
      border-radius: $border-radius-md;
      font-weight: 600;
      transition: all 0.2s ease;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }
    }
  }
}

// ===== HEADER STYLES =====
.onboarding-header {
  background: linear-gradient(
    135deg,
    $onboarding-primary 0%,
    $onboarding-secondary 100%
  );
  color: white;
  padding: $spacing-lg;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    position: relative;
    z-index: 1;
  }

  .header-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);

    .q-icon {
      font-size: 24px;
      color: white;
    }
  }

  .header-text {
    flex: 1;

    .title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 4px;
      line-height: 1.2;
    }

    .subtitle {
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.4;
    }
  }
}

// ===== STEP INDICATOR =====
.onboarding-steps {
  background: $onboarding-gray-50;
  padding: $spacing-md $spacing-lg;
  border-bottom: 1px solid $onboarding-gray-200;

  .steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-md;
    max-width: 600px;
    margin: 0 auto;
  }

  .step-item {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;

      &.active {
        background: $onboarding-primary;
        color: white;
        box-shadow: 0 0 0 4px rgba($onboarding-primary, 0.2);
      }

      &.completed {
        background: $onboarding-success;
        color: white;
      }

      &.inactive {
        background: $onboarding-gray-200;
        color: $onboarding-gray-500;
      }
    }

    .step-label {
      font-size: 14px;
      font-weight: 500;
      color: $onboarding-gray-700;

      &.active {
        color: $onboarding-primary;
      }

      &.completed {
        color: $onboarding-success;
      }
    }

    .step-connector {
      width: 40px;
      height: 2px;
      background: $onboarding-gray-200;

      &.completed {
        background: $onboarding-success;
      }
    }
  }
}

// ===== CONTENT SECTION =====
.onboarding-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: $spacing-xl 0;
  background: transparent;

  .content-header {
    text-align: center;
    margin-bottom: $spacing-xl;
    padding: 0 $spacing-lg;

    .content-title {
      font-size: 36px;
      font-weight: 700;
      color: $onboarding-gray-900;
      margin-bottom: $spacing-lg;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-lg;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 28px;
        flex-direction: column;
        gap: $spacing-md;
      }

      .title-icon {
        width: 56px;
        height: 56px;
        border-radius: $border-radius-xl;
        background: linear-gradient(
          135deg,
          $onboarding-primary 0%,
          $onboarding-secondary 100%
        );
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: $shadow-lg;

        @media (max-width: 768px) {
          width: 48px;
          height: 48px;
        }

        .q-icon {
          color: white;
          font-size: 28px;

          @media (max-width: 768px) {
            font-size: 24px;
          }
        }
      }
    }

    .content-subtitle {
      font-size: 20px;
      color: $onboarding-gray-600;
      line-height: 1.6;
      max-width: 700px;
      margin: 0 auto;
      font-weight: 400;

      @media (max-width: 768px) {
        font-size: 18px;
      }
    }
  }

  .content-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;

    margin: 0 $spacing-md;
    padding: $spacing-xl;

    @media (max-width: 768px) {
      margin: 0 $spacing-md;
      border-radius: $border-radius-lg;
      padding: $spacing-lg;
    }
  }
}

// ===== FORM ELEMENTS =====
.onboarding-form {
  .form-section {
    margin-bottom: $spacing-xl;

    .section-label {
      font-size: 18px;
      font-weight: 600;
      color: $onboarding-gray-900;
      margin-bottom: $spacing-sm;
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      .label-icon {
        width: 24px;
        height: 24px;
        border-radius: $border-radius-sm;
        background: $onboarding-gray-100;
        display: flex;
        align-items: center;
        justify-content: center;

        .q-icon {
          font-size: 14px;
          color: $onboarding-gray-600;
        }
      }
    }

    .section-description {
      font-size: 16px;
      color: $onboarding-gray-600;
      margin-bottom: $spacing-lg;
      line-height: 1.6;
    }
  }

  .form-grid {
    display: grid;
    gap: $spacing-lg;

    &.grid-2 {
      grid-template-columns: 1fr 1fr;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    &.grid-3 {
      grid-template-columns: repeat(3, 1fr);

      @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }
  }

  .form-actions {
    margin-top: $spacing-xl;
    display: flex;
    justify-content: center;
    gap: $spacing-md;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  // Enhanced input styles
  .q-field {
    // margin-bottom: $spacing-lg;

    .q-field__control {
      border-radius: $border-radius-md;
      box-shadow: $shadow-sm;

      min-height: 56px;

      &:hover {
        box-shadow: $shadow-md;
        border-color: $onboarding-gray-300;
      }
    }

    .q-field__label {
      font-weight: 500;
      color: $onboarding-gray-700;
    }

    .q-field__prepend {
      .q-icon {
        font-size: 20px;
      }
    }
  }

  // Enhanced button styles
  .q-btn {
    border-radius: $border-radius-lg;
    font-weight: 600;
    transition: all 0.3s ease;
    min-height: 48px;
    position: relative;
    overflow: hidden;

    &.btn-primary {
      background: linear-gradient(135deg, 0%, $onboarding-secondary 100%);
      box-shadow: $shadow-md;
      color: white;

      &:hover {
        box-shadow: $shadow-xl;
      }
    }

    &.btn-secondary {
      background: white;
      color: $onboarding-gray-700;
      border: 2px solid $onboarding-gray-300;

      &:hover {
        background: $onboarding-gray-50;
        border-color: $onboarding-primary;
        color: $onboarding-primary;
      }
    }

    &.btn-manny {
      background: linear-gradient(
        135deg,
        $onboarding-secondary 0%,
        $onboarding-accent 100%
      );
      color: white;

      &:hover {
        box-shadow: $shadow-xl;
      }

      &:active {
        transform: translateY(0);
      }
    }

    &.btn-success {
      background: linear-gradient(135deg, $onboarding-success 0%, #22c55e 100%);
      color: white;
      box-shadow: $shadow-md;

      &:hover {
        box-shadow: $shadow-xl;
      }
    }

    &.btn-warning {
      background: linear-gradient(135deg, $onboarding-warning 0%, #f59e0b 100%);
      color: white;
      box-shadow: $shadow-md;

      &:hover {
        box-shadow: $shadow-xl;
      }
    }

    &.btn-error {
      background: linear-gradient(135deg, $onboarding-error 0%, #dc2626 100%);
      color: white;
      box-shadow: $shadow-md;

      &:hover {
        box-shadow: $shadow-xl;
      }
    }

    &.btn-ghost {
      background: transparent;
      color: $onboarding-primary;
      border: 2px solid transparent;

      &:hover {
        background: rgba($onboarding-primary, 0.1);
        border-color: $onboarding-primary;
      }
    }

    &.btn-outline {
      background: transparent;
      border: 2px solid currentColor;

      &:hover {
        background: currentColor;
        color: white;
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;

      &:hover {
        transform: none !important;
        box-shadow: none !important;
      }

      &::before {
        display: none;
      }
    }

    // Size variants
    &.btn-sm {
      min-height: 36px;
      padding: $spacing-sm $spacing-md;
      font-size: 14px;
    }

    &.btn-lg {
      min-height: 56px;
      padding: $spacing-md $spacing-xl;
      font-size: 18px;
    }

    &.btn-xl {
      min-height: 64px;
      padding: $spacing-lg $spacing-xl;
      font-size: 20px;
    }
  }
}

// ===== MANNY ASSISTANT STYLES =====
.manny-assistant {
  .manny-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;

    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &.loading {
      &::after {
        content: '';
        position: absolute;
        width: 50px;
        height: 50px;
        border: 2px solid rgba($onboarding-secondary, 0.3);
        border-radius: 50%;
        border-top-color: $onboarding-secondary;
        animation: spin 1s linear infinite;
      }
    }
  }

  .manny-button {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    padding: $spacing-sm $spacing-md;
    background: linear-gradient(
      135deg,
      $onboarding-secondary 0%,
      $onboarding-accent 100%
    );
    color: white;
    border-radius: $border-radius-md;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-lg;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .manny-icon {
      width: 20px;
      height: 20px;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// ===== CHAPTER MANAGEMENT =====
.onboarding-chapters {
  .chapter-list {
    .chapter-item {
      background: white;
      border: 1px solid $onboarding-gray-200;
      border-radius: $border-radius-md;
      margin-bottom: $spacing-md;
      transition: all 0.2s ease;

      &:hover {
        border-color: $onboarding-primary;
        box-shadow: $shadow-md;
      }

      .chapter-header {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        padding: $spacing-md;

        .drag-handle {
          color: $onboarding-gray-400;
          cursor: grab;

          &:active {
            cursor: grabbing;
          }
        }
        .chapter-content {
          flex: 1;
          align-items: center;
        }
        .chapter-number {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: $onboarding-gray-100;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          color: $onboarding-gray-700;
        }
      }

      .chapter-actions {
        display: flex;
        gap: $spacing-sm;

        .action-btn {
          padding: $spacing-sm;
          border-radius: $border-radius-sm;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;

          &.delete-btn {
            background: rgba($onboarding-error, 0.1);
            color: $onboarding-error;

            &:hover {
              background: rgba($onboarding-error, 0.2);
            }
          }
        }
      }
    }
  }

  .add-chapter-btn {
    width: 100%;
    padding: $spacing-lg;
    border: 2px dashed $onboarding-gray-300;
    border-radius: $border-radius-md;
    background: $onboarding-gray-50;
    color: $onboarding-gray-500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-sm;

    &:hover {
      border-color: $onboarding-primary;
      background: rgba($onboarding-primary, 0.05);
      color: $onboarding-primary;
    }

    .add-icon {
      font-size: 20px;
    }
  }
}

// ===== OPTION GROUPS =====
.onboarding-options {
  .option-group {
    display: grid;
    gap: $spacing-sm;
    margin-bottom: $spacing-md;

    &.grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    &.grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .option-item {
      padding: $spacing-md;
      border: 2px solid $onboarding-gray-200;
      border-radius: $border-radius-md;
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;

      &:hover {
        border-color: $onboarding-primary;
        background: rgba($onboarding-primary, 0.05);
      }

      &.selected {
        border-color: $onboarding-primary;
        background: rgba($onboarding-primary, 0.1);
        color: $onboarding-primary;
      }

      .option-icon {
        font-size: 24px;
        margin-bottom: $spacing-sm;
        color: $onboarding-gray-500;
      }

      .option-label {
        font-weight: 600;
        margin-bottom: $spacing-xs;
      }

      .option-description {
        font-size: 14px;
        color: $onboarding-gray-500;
        line-height: 1.4;
      }
    }
  }
}

// ===== PROGRESS INDICATORS =====
.onboarding-progress {
  .progress-bar {
    width: 100%;
    height: 8px;
    background: $onboarding-gray-200;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: $spacing-md;

    .progress-fill {
      height: 100%;
      background: linear-gradient(
        90deg,
        $onboarding-primary 0%,
        $onboarding-secondary 100%
      );
      transition: width 0.3s ease;
      border-radius: 4px;
    }
  }

  .progress-text {
    font-size: 14px;
    color: $onboarding-gray-600;
    text-align: center;
  }
}

// ===== LOADING STATES =====
.onboarding-loading {
  .loading-skeleton {
    .skeleton-line {
      height: 16px;
      background: linear-gradient(
        90deg,
        $onboarding-gray-200 25%,
        $onboarding-gray-100 50%,
        $onboarding-gray-200 75%
      );
      background-size: 200% 100%;
      border-radius: 4px;
      margin-bottom: $spacing-sm;
      animation: shimmer 2s infinite;

      &.short {
        width: 60%;
      }

      &.medium {
        width: 80%;
      }

      &.long {
        width: 100%;
      }
    }
  }

  .loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid $onboarding-gray-200;
      border-top-color: $onboarding-primary;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// ===== ACTION BUTTONS =====
.onboarding-actions {
  padding: $spacing-lg;
  background: $onboarding-gray-50;
  border-top: 1px solid $onboarding-gray-200;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-md;

  .action-group {
    display: flex;
    gap: $spacing-sm;

    .action-btn {
      padding: $spacing-sm $spacing-lg;
      border-radius: $border-radius-md;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;

      &.btn-secondary {
        background: white;
        color: $onboarding-gray-700;
        border: 1px solid $onboarding-gray-300;

        &:hover {
          background: $onboarding-gray-50;
          border-color: $onboarding-gray-400;
        }
      }

      &.btn-primary {
        background: linear-gradient(
          135deg,
          $onboarding-primary 0%,
          $onboarding-secondary 100%
        );
        color: white;
        box-shadow: $shadow-md;

        &:hover {
          transform: translateY(-1px);
          box-shadow: $shadow-lg;
        }
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
      }
    }
  }
}

// ===== MOBILE RESPONSIVE =====
@media (max-width: 768px) {
  .onboarding-container {
    margin: 0;
    border-radius: 0;
    height: 100vh;

    .q-card {
      border-radius: 0;
      height: 100%;
    }
  }

  .onboarding-header {
    padding: $spacing-md;

    .header-content {
      gap: $spacing-sm;
    }

    .header-icon {
      width: 40px;
      height: 40px;
    }

    .header-text {
      .title {
        font-size: 18px;
      }

      .subtitle {
        font-size: 13px;
      }
    }
  }

  .onboarding-steps {
    padding: $spacing-sm $spacing-md;

    .steps-container {
      gap: $spacing-sm;
    }

    .step-item {
      .step-number {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }

      .step-label {
        font-size: 12px;
      }

      .step-connector {
        width: 20px;
      }
    }
  }

  .onboarding-content {
    padding: $spacing-md;

    .content-header {
      margin-bottom: $spacing-md;

      .content-title {
        font-size: 20px;

        .title-icon {
          width: 28px;
          height: 28px;
        }
      }

      .content-subtitle {
        font-size: 14px;
      }
    }
  }

  .onboarding-form {
    .form-section {
      margin-bottom: $spacing-md;

      .section-label {
        font-size: 14px;
      }
    }
  }

  .onboarding-options {
    .option-group {
      &.grid-2,
      &.grid-3 {
        grid-template-columns: 1fr;
      }

      .option-item {
        padding: $spacing-sm;

        .option-icon {
          font-size: 20px;
        }
      }
    }
  }

  .onboarding-actions {
    padding: $spacing-md;
    flex-direction: column;
    gap: $spacing-sm;

    .action-group {
      width: 100%;
      justify-content: center;

      .action-btn {
        flex: 1;
        min-width: 0;
      }
    }
  }

  .onboarding-chapters {
    .chapter-item {
      .chapter-header {
        padding: $spacing-sm;

        .chapter-number {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }
      }
    }
  }
}

// ===== TABLET RESPONSIVE =====
@media (min-width: 769px) and (max-width: 1024px) {
  .onboarding-content {
    padding: $spacing-lg;
  }

  .onboarding-options {
    .option-group {
      &.grid-3 {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

// ===== ACCESSIBILITY =====
.onboarding-container {
  // Focus styles
  .q-btn:focus,
  .q-field:focus-within,
  .option-item:focus {
    outline: 2px solid $onboarding-primary;
    outline-offset: 2px;
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    .onboarding-header {
      background: $onboarding-gray-900;
    }

    .option-item {
      border-width: 3px;

      &.selected {
        background: $onboarding-primary;
        color: white;
      }
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// ===== MODAL SPECIFIC STYLES =====
.onboarding-modal {
  .q-dialog__inner {
    padding: $spacing-md;
  }

  .q-card {
    border-radius: $border-radius-xl;
    box-shadow: $shadow-xl;
    max-width: 95vw;
    max-height: 95vh;
    overflow: hidden;

    @media (max-width: 768px) {
      border-radius: $border-radius-lg;
      max-width: 100vw;
      max-height: 100vh;
      margin: 0;
    }
  }

  .modal-header {
    background: linear-gradient(
      135deg,
      $onboarding-primary 0%,
      $onboarding-secondary 100%
    );
    color: white;
    padding: $spacing-lg $spacing-xl;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .modal-title {
      font-size: 24px;
      font-weight: 700;
      margin: 0;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .title-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);

        .q-icon {
          font-size: 20px;
          color: white;
        }
      }
    }

    .modal-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin-top: $spacing-sm;
      position: relative;
      z-index: 1;
    }
  }

  .modal-content {
    padding: $spacing-xl;
    flex: 1;
    overflow-y: auto;

    @media (max-width: 768px) {
      padding: $spacing-lg;
    }
  }

  .modal-actions {
    padding: $spacing-lg $spacing-xl;
    background: $onboarding-gray-50;
    border-top: 1px solid $onboarding-gray-200;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-md;

    @media (max-width: 768px) {
      padding: $spacing-md;
      flex-direction: column;
      gap: $spacing-sm;

      .action-group {
        width: 100%;
        display: flex;
        gap: $spacing-sm;

        .q-btn {
          flex: 1;
        }
      }
    }
  }
}

// ===== ENHANCED STEPPER STYLES =====
.onboarding-stepper {
  .stepper-header {
    background: white;
    border-bottom: 2px solid $onboarding-gray-100;
    padding: $spacing-lg 0;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .stepper-content {
    min-height: 400px;
    padding: $spacing-xl 0;
  }

  .step-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg 0;
    border-top: 1px solid $onboarding-gray-200;
    background: white;
    position: sticky;
    bottom: 0;
    z-index: 10;

    .nav-group {
      display: flex;
      gap: $spacing-sm;
    }
  }
}

// ===== ENHANCED ICON STYLES =====
.onboarding-icon {
  &.icon-sm {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }

  &.icon-md {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  &.icon-lg {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }

  &.icon-xl {
    width: 64px;
    height: 64px;
    font-size: 32px;
  }

  &.icon-rounded {
    border-radius: 50%;
  }

  &.icon-square {
    border-radius: $border-radius-md;
  }

  &.icon-primary {
    background: linear-gradient(
      135deg,
      $onboarding-primary 0%,
      $onboarding-secondary 100%
    );
    color: white;
  }

  &.icon-secondary {
    background: $onboarding-gray-100;
    color: $onboarding-gray-600;
  }

  &.icon-success {
    background: linear-gradient(135deg, $onboarding-success 0%, #22c55e 100%);
    color: white;
  }

  &.icon-warning {
    background: linear-gradient(135deg, $onboarding-warning 0%, #f59e0b 100%);
    color: white;
  }

  &.icon-error {
    background: linear-gradient(135deg, $onboarding-error 0%, #dc2626 100%);
    color: white;
  }

  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-md;
  }
}

// ===== UTILITY CLASSES =====
.onboarding-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.onboarding-slide-up {
  animation: slideUp 0.3s ease-out;
}

.onboarding-bounce {
  animation: bounce 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// ===== CONSOLIDATED COMPONENT STYLES =====

// ===== MANNY BUTTON STYLES (USED ACROSS ALL COMPONENTS) =====
.manny-button {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  background: $onboarding-primary;
  color: white;
  border-radius: $border-radius-md;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: $shadow-lg;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &.round {
    padding: 8px;
    border-radius: 50%;
  }

  .manny-avatar {
    width: 24px;
    height: 24px;

    display: flex;
    align-items: center;
    justify-content: center;

    &.loading::after {
      content: '';
      position: absolute;
      width: 28px;
      height: 28px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s linear infinite;
    }

    &.large {
      width: 32px;
      height: 32px;

      &.loading::after {
        width: 36px;
        height: 36px;
      }
    }
  }

  &.generate-btn {
    padding: $spacing-md $spacing-xl;
    font-size: 16px;
    font-weight: 600;
    border-radius: $border-radius-lg;

    .manny-avatar {
      margin-right: $spacing-sm;
      width: 20px;
      height: 20px;
    }
  }
}

// ===== OPTION ITEM STYLES (USED IN TITLE/SUBTITLE COMPONENTS) =====
.option-item {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: $spacing-md;
  border: 2px solid $onboarding-gray-200;
  border-radius: $border-radius-md;
  background: white;
  text-align: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-md;
    border-color: $onboarding-primary;
    background: rgba($onboarding-primary, 0.05);
  }

  &.selected {
    border-color: $onboarding-primary;
    background: linear-gradient(
      135deg,
      rgba($onboarding-primary, 0.1) 0%,
      rgba($onboarding-secondary, 0.05) 100%
    );
    color: $onboarding-primary;
  }

  .option-icon {
    font-size: 24px;
    margin-bottom: $spacing-sm;
    color: $onboarding-secondary;
  }

  .option-label {
    font-weight: 600;
    margin-bottom: $spacing-xs;
  }

  .option-description {
    font-size: 12px;
    color: $onboarding-gray-500;
    margin-top: 4px;
    line-height: 1.4;
  }
}

// ===== TITLE SUBTITLE STEP STYLES =====

.onboarding-content {
  .content-header {
    margin-bottom: $spacing-xl;

    .content-title {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      font-size: 24px;
      font-weight: 600;
      color: $onboarding-gray-900;
      margin-bottom: $spacing-sm;

      .title-icon {
        width: 32px;
        height: 32px;
        border-radius: $border-radius-md;
        background: linear-gradient(
          135deg,
          $onboarding-primary 0%,
          $onboarding-secondary 100%
        );
        display: flex;
        align-items: center;
        justify-content: center;

        .q-icon {
          color: white;
          font-size: 18px;
        }
      }
    }

    .content-subtitle {
      font-size: 16px;
      color: $onboarding-gray-500;
      line-height: 1.5;
    }
  }

  .input-section {
    margin-bottom: $spacing-xl;

    .input-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: $spacing-md;
      margin-bottom: $spacing-lg;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }

    .generate-section {
      display: flex;
      justify-content: center;
      margin-top: $spacing-lg;
    }
  }

  .suggestions-section {
    // Loading State
    .suggestions-loading {
      .loading-skeleton {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: $spacing-lg;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
          gap: $spacing-md;
        }

        .skeleton-column {
          .skeleton-header {
            height: 24px;
            background: linear-gradient(
              90deg,
              $onboarding-gray-200 25%,
              $onboarding-gray-100 50%,
              $onboarding-gray-200 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: $border-radius-sm;
            margin-bottom: $spacing-md;
            width: 60%;
          }

          .skeleton-item {
            height: 48px;
            background: linear-gradient(
              90deg,
              $onboarding-gray-200 25%,
              $onboarding-gray-100 50%,
              $onboarding-gray-200 75%
            );
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: $border-radius-md;
            margin-bottom: $spacing-sm;

            &:nth-child(2) {
              animation-delay: 0.1s;
            }
            &:nth-child(3) {
              animation-delay: 0.2s;
            }
            &:nth-child(4) {
              animation-delay: 0.3s;
            }
          }
        }
      }
    }

    // Empty State
    .suggestions-empty {
      text-align: center;
      padding: $spacing-xl;
      color: $onboarding-gray-500;

      .empty-icon {
        font-size: 48px;
        margin-bottom: $spacing-md;
        opacity: 0.6;

        .q-icon {
          color: $onboarding-gray-400;
        }
      }

      .empty-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: $spacing-sm;
        color: $onboarding-gray-600;
      }

      .empty-description {
        font-size: 14px;
        line-height: 1.5;
        max-width: 300px;
        margin: 0 auto;
      }
    }

    // Suggestions Grid
    .suggestions-grid {
      display: grid;
      gap: $spacing-lg;

      &.grid-2 {
        grid-template-columns: 1fr 1fr;
      }

      &.grid-3 {
        grid-template-columns: 1fr 1fr 1fr;
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr !important;
        gap: $spacing-md;
      }

      .suggestion-column {
        background: rgba(white, 0.8);
        border-radius: $border-radius-lg;
        padding: $spacing-md;
        border: 1px solid $onboarding-gray-100;
        transition: all 0.3s ease;

        &.has-selections {
          border-color: rgba($onboarding-primary, 0.3);
          background: rgba($onboarding-primary, 0.02);
        }

        .column-header {
          margin-bottom: $spacing-md;
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          padding-bottom: $spacing-sm;
          border-bottom: 2px solid $onboarding-gray-100;

          .column-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(
              135deg,
              $onboarding-primary 0%,
              $onboarding-secondary 100%
            );
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: $shadow-sm;

            .q-icon {
              font-size: 16px;
              color: white;
            }
          }

          .column-title {
            font-size: 18px;
            font-weight: 700;
            color: $onboarding-gray-900;
            flex: 1;

            .suggestion-count {
              font-size: 14px;
              font-weight: 500;
              color: $onboarding-gray-500;
              margin-left: $spacing-xs;
            }
          }
        }

        .suggestion-list {
          &.compact .suggestion-item {
            padding: $spacing-sm $spacing-md;
            margin-bottom: $spacing-xs;
          }

          .suggestion-item {
            padding: $spacing-md;
            border: 2px solid $onboarding-gray-200;
            border-radius: $border-radius-md;
            margin-bottom: $spacing-sm;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(
                90deg,
                transparent,
                rgba($onboarding-primary, 0.1),
                transparent
              );
              transition: left 0.5s;
            }

            &:hover {
              border-color: $onboarding-primary;
              box-shadow: $shadow-md;
              transform: translateY(-2px);
              background: rgba($onboarding-primary, 0.02);

              &::before {
                left: 100%;
              }
            }

            &:focus {
              outline: none;
              border-color: $onboarding-primary;
              box-shadow: 0 0 0 3px rgba($onboarding-primary, 0.2);
            }

            &.selected {
              border-color: $onboarding-primary;
              background: linear-gradient(
                135deg,
                rgba($onboarding-primary, 0.1) 0%,
                rgba($onboarding-secondary, 0.05) 100%
              );
              transform: translateY(-1px);
              box-shadow: $shadow-lg;

              .suggestion-text {
                color: $onboarding-primary;
                font-weight: 600;
              }

              .suggestion-description {
                color: rgba($onboarding-primary, 0.8);
              }

              .check-icon {
                color: $onboarding-primary;
                transform: scale(1.1);
              }
            }

            .suggestion-content {
              display: flex;
              align-items: flex-start;
              justify-content: space-between;
              gap: $spacing-sm;

              .suggestion-main {
                flex: 1;

                .suggestion-text {
                  font-size: 15px;
                  line-height: 1.4;
                  color: $onboarding-gray-800;
                  font-weight: 500;
                  margin-bottom: $spacing-xs;
                }

                .suggestion-description {
                  font-size: 13px;
                  line-height: 1.3;
                  color: $onboarding-gray-600;
                }
              }

              .suggestion-actions {
                display: flex;
                align-items: center;
                margin-top: 2px;

                .check-icon {
                  font-size: 20px;
                  opacity: 0;
                  transition: all 0.3s ease;
                  color: $onboarding-gray-400;
                }
              }
            }

            &.selected .check-icon {
              opacity: 1;
            }

            &.has-description {
              .suggestion-content {
                align-items: flex-start;
              }
            }
          }
        }
      }
    }
  }
}

// ===== THEME-SPECIFIC OVERRIDES =====

// Faith Story Theme
.faith-story-theme {
  .title-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important;
  }

  .section-label .label-icon {
    background: linear-gradient(
      135deg,
      rgba(139, 92, 246, 0.1) 0%,
      rgba(168, 85, 247, 0.1) 100%
    );

    .q-icon {
      color: #8b5cf6;
    }
  }

  .chapter-number {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important;
  }

  .chapter-item:hover {
    border-color: #8b5cf6 !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15) !important;
  }

  .add-chapter-btn {
    border-color: rgba(139, 92, 246, 0.3);

    &:hover {
      border-color: #8b5cf6;
      background: rgba(139, 92, 246, 0.05);
      color: #8b5cf6;
    }
  }

  .loading-text {
    color: #8b5cf6;
  }

  .chapter-actions-header {
    background: linear-gradient(
      135deg,
      rgba(139, 92, 246, 0.05) 0%,
      rgba(168, 85, 247, 0.05) 100%
    );
    border: 1px solid rgba(139, 92, 246, 0.2);
  }

  .onboarding-header {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  }
}

// Autobiography Theme
.autobiography-theme {
  .title-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  }

  .section-label .label-icon {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.1) 0%,
      rgba(217, 119, 6, 0.1) 100%
    );

    .q-icon {
      color: #f59e0b;
    }
  }

  .chapter-number {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  }

  .chapter-item:hover {
    border-color: #f59e0b !important;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.15) !important;
  }

  .add-chapter-btn {
    border-color: rgba(245, 158, 11, 0.3);

    &:hover {
      border-color: #f59e0b;
      background: rgba(245, 158, 11, 0.05);
      color: #f59e0b;
    }
  }

  .loading-text {
    color: #f59e0b;
  }

  .chapter-actions-header {
    background: linear-gradient(
      135deg,
      rgba(245, 158, 11, 0.05) 0%,
      rgba(217, 119, 6, 0.05) 100%
    );
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  .onboarding-header {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }
}
