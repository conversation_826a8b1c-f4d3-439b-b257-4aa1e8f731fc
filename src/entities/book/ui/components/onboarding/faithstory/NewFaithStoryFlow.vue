<template>
  <q-dialog v-model="onBoardingActive" persistent>
    <q-card
      :style="{
        maxWidth: '100vw',
        width: $q.screen.gt.xs ? '60vw' : '100%',
        minHeight: '90%',
        overflow: 'hidden',
      }"
    >
      <!-- Header -->
      <OnboardingHeader
        title="Book Foundations"
        subtitle="Create your faith story foundation"
        icon="img:robot.png"
        :show-actions="true"
        :auto-save="autosave"
        :show-save-button="!autosave"
        :saving="false"
        @save="complete"
      >
        <template #actions>
          <q-btn
            dense
            flat
            icon="close"
            @click="cancel"
            v-if="autosave === true"
          >
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </template>
      </OnboardingHeader>

      <!-- Step Indicator -->
      <OnboardingStepIndicator
        :steps="stepIndicatorSteps"
        :current-step="step"
      />

      <!-- Main Content with Scrolling -->
      <div class="onboarding-content-container">
        <q-scroll-area
          :thumb-style="thumbStyle"
          :bar-style="barStyle"
          :style="{
            height: `calc(100vh - ${isScreenBiggerMd ? '320px' : '210px'})`,
          }"
        >
          <!-- Step 1 - Mission statement creation -->
          <div v-if="step === Steps.CreateMission">
            <FaithStoryMissionStep
              :updating="updating"
              :isScreenBiggerMd="isScreenBiggerMd"
              v-model:questions="missionQuestions"
            />
          </div>

          <!-- Step 2 - Choose a title -->
          <div v-if="step === Steps.WriteTitle">
            <FaithStoryTitleSubtitleStep
              v-model:titleModelValue="title"
              v-model:subtitleModelValue="subtitle"
              v-model:questions="missionQuestions"
              :updating="updating"
            />
          </div>

          <!-- Step 3 - Chapters -->
          <div v-if="step === Steps.Chapters">
            <FaithStoryChaptersStep
              v-model="chapters"
              :title="title"
              :subtitle="subtitle"
              :isScreenBiggerMd="isScreenBiggerMd"
              v-model:autosave="autosave"
              v-model:questions="missionQuestions"
              @complete="complete"
            />
          </div>

          <!-- Step 4 - Recap -->
          <div v-if="step === Steps.Recap">
            <OnboardingStepWrapper
              theme="default"
              content-title="Faith Story Foundation Summary"
              content-subtitle="Review and finalize your faith story book foundation."
              content-icon="title"
              :show-actions="false"
            >
              <div class="recap-content">
                <div class="recap-item">
                  <div class="recap-label">
                    <q-btn
                      round
                      flat
                      icon="edit"
                      size="sm"
                      @click="step = Steps.WriteTitle"
                    />
                    Working title:
                  </div>
                  <div class="recap-value text-italic">
                    {{ title || 'Not set' }}
                  </div>
                </div>

                <div class="recap-item">
                  <div class="recap-label">
                    <q-btn
                      round
                      flat
                      icon="edit"
                      size="sm"
                      @click="step = Steps.WriteTitle"
                    />
                    Working subtitle:
                  </div>
                  <div class="recap-value text-italic">
                    {{ subtitle || 'Not set' }}
                  </div>
                </div>

                <div class="recap-item">
                  <div class="recap-label">
                    <q-btn
                      round
                      flat
                      icon="edit"
                      size="sm"
                      @click="step = Steps.Chapters"
                    />
                    Chapter outline:
                  </div>
                  <div class="recap-value">
                    <ol v-if="chapters.length > 0">
                      <li
                        v-for="chapter in chapters"
                        :key="chapter.id"
                        class="text-subtitle2"
                      >
                        {{ chapter.title }}
                      </li>
                    </ol>
                    <div v-else class="text-grey-6">No chapters added yet</div>
                  </div>
                </div>
              </div>
            </OnboardingStepWrapper>
          </div>
        </q-scroll-area>
      </div>
      <!-- Action Buttons -->
      <OnboardingActionButtons
        :show-back-button="step > Steps.CreateMission"
        :show-skip-button="step === Steps.WriteTitle"
        :show-next-button="step < Steps.Recap"
        :show-complete-button="step === Steps.Recap"
        :can-proceed="canProceedToNext"
        :loading="false"
        back-label="Back"
        skip-label="Skip to Recap"
        next-label="Next"
        complete-label="Save"
        next-icon="arrow_forward"
        complete-icon="check"
        @back="goToPreviousStep"
        @skip="step = Steps.Recap"
        @next="goToNextStep"
        @complete="complete"
      >
        <template
          #center-actions
          v-if="!isScreenBiggerMd && step === Steps.Recap"
        >
          <q-btn @click="cancel" color="danger" outline label="Cancel">
            <q-tooltip>Do not save changes.</q-tooltip>
          </q-btn>
        </template>
      </OnboardingActionButtons>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  onUnmounted,
  ref,
  toRef,
  unref,
  watch,
} from 'vue';

import FaithStoryMissionStep, {
  type Questions as MissionQuestions,
} from './FaithStoryMissionStep.vue';
import FaithStoryTitleSubtitleStep from './FaithStoryTitleSubtitleStep.vue';
import FaithStoryChaptersStep from './FaithStoryChaptersStep.vue';

import {
  Book,
  NewBook,
  ChapterOutline,
  FaithStoryMissionQuestions,
} from 'src/entities/book/model/types';
import { listOutlines, setBook, updateBook } from 'src/entities/book';

import { barStyle, thumbStyle } from 'src/entities/setting';
import { confirmOverrideText } from 'src/shared/lib/quasar-dialogs';
import { useQuasar } from 'quasar';

import { ACTIONS, loggingService, PAGES } from 'src/entities/log';
import { useVModel, watchDebounced } from '@vueuse/core';
import { useRouter } from 'vue-router';

// Import shared components
import {
  OnboardingHeader,
  OnboardingStepIndicator,
  OnboardingContentHeader,
  OnboardingFormSection,
  OnboardingActionButtons,
  type OnboardingStep,
  OnboardingStepWrapper,
} from '../shared';

export type NewBookStarter = Pick<
  NewBook,
  'title' | 'subtitle' | 'mission' | 'missionQuestions' | 'chapters'
>;

export type ExistingBookStarter = Book;
export type ExistingBookOutlinesStarter = ChapterOutline[];

const props = defineProps<{
  modal: boolean;
  initialValues?: ExistingBookStarter;
  initialOutlines?: ExistingBookOutlinesStarter;
  shouldUpdate?: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  completed: [book: NewBookStarter];
  updated: [id: string, book: ExistingBookStarter];
}>();

const onBoardingActive = ref(props.modal);
const router = useRouter();
// Reactive references for browser width and div size
const browserWidth = ref(window.innerHeight);
const isScreenBiggerMd = computed(
  () => $q.screen.gt.sm && browserWidth.value > 650,
);
// Function to update the browser width
const updateWidth = () => {
  browserWidth.value = window.innerHeight;
};
const $q = useQuasar();

onMounted(() => {
  // Update browser width on resize
  window.addEventListener('resize', updateWidth);
});

onUnmounted(() => {
  // Clean up event listeners and observers
  window.removeEventListener('resize', updateWidth);
});

enum Steps {
  CreateMission,
  WriteTitle,
  Chapters,
  Recap,
}

const step = ref<Steps>(Steps.CreateMission);
const initialValues = toRef(props, 'initialValues');
const initialOutlines = toRef(props, 'initialOutlines');
const shouldUpdate = toRef(props, 'shouldUpdate');
// const autosave = useVModel(props, 'autosave', emit);
const autosave = ref(true);

const updating = computed(() => initialValues.value != undefined);
const missionQuestions = ref<FaithStoryMissionQuestions>(
  (initialValues.value?.missionQuestions as FaithStoryMissionQuestions) ?? {
    gender: 'Both',
    age: [],
    description: '',
    bestDescription: 'A personal testimony of coming to faith',
    otherBestDescription: '',
    keyLifeEvents: '',
    readersTakeaway: '',
    mainReason: '',
    bookOrder: 'Chronological',
    bookFocused: '',
    authorImpact: '',
  },
);

const mission = ref(initialValues.value?.mission ?? '');
const title = ref(initialValues.value?.title ?? '');
const subtitle = ref(initialValues.value?.subtitle ?? '');

let chaptersList = [];

if (!initialOutlines.value?.length) {
  const outlines = initialValues.value?.id
    ? unref(await listOutlines(initialValues.value?.id))
    : null;

  if (outlines?.length && outlines?.outlines?.length) {
    // autosave.value = false;
    chaptersList = outlines?.outlines;
  } else {
    // autosave.value = true;
    chaptersList = initialValues.value?.chapters || [];
  }
} else {
  chaptersList = initialOutlines.value;
}

const chapters = ref<any[]>(chaptersList);

// Step indicator configuration
const stepIndicatorSteps = computed<OnboardingStep[]>(() => [
  { id: 'mission', label: 'Mission Statement' },
  { id: 'title', label: 'Title & Subtitle' },
  { id: 'chapters', label: 'Chapters' },
  { id: 'recap', label: 'Recap' },
]);

// Navigation helpers
const canProceedToNext = computed(() => {
  switch (step.value) {
    case Steps.CreateMission:
      return missionQuestions.value.authorImpact.length > 0;
    case Steps.WriteTitle:
      return title.value.length > 0 && subtitle.value.length > 0;
    case Steps.Chapters:
      return chapters.value.length > 0;
    case Steps.Recap:
      return true;
    default:
      return false;
  }
});

const goToPreviousStep = () => {
  if (step.value > Steps.CreateMission) {
    step.value = step.value - 1;
  }
};

const goToNextStep = () => {
  if (step.value < Steps.Recap) {
    step.value = step.value + 1;
  }
};

watch(
  step,
  async (selectedStep) => {
    let selected = 'Mission statement';
    let aSave = true;
    switch (selectedStep) {
      case 1: {
        selected = 'Title and Subtitle';
        break;
      }
      case 2: {
        selected = 'Chapters';
        aSave = true;
        break;
      }
      case 3: {
        selected = 'Recap';
        aSave = false;
        break;
      }
      default: {
        selected = 'Mission statement';
        break;
      }
    }
    autosave.value = aSave;

    await loggingService.logAction(
      PAGES.BOOKMISSIONSTATEMENT,
      ACTIONS.CLICK,
      `Step in Book Foundation is selected: ${selected}`,
      MODULE_NAME,
      initialValues.value?.id as string,
    );
  },
  { immediate: true },
);
watch(
  props,
  (newVal) => {
    onBoardingActive.value = newVal.modal as boolean;
  },
  { immediate: true },
);
// Define the module or component name
const MODULE_NAME = 'book_foundations';

const cancel = () => {
  if (shouldUpdate.value === true) {
    emit('updated', initialValues.value?.id, {
      ...initialValues.value,
      id: initialValues.value?.id,
      mission: mission.value,
      title: title.value,
      subtitle: subtitle.value,
      chapters: chapters.value,
      missionQuestions: missionQuestions.value,
    });
  } else {
    emit('cancel');
  }
  if (onBoardingActive.value === true) {
    onBoardingActive.value = false;
  }
};
async function complete() {
  if (initialValues.value && initialValues.value?.id) {
    const shouldSave = await confirmOverrideText($q, {
      message: `Are you sure you want to save the changes?`,
    });
    if (!shouldSave) {
      return;
    }
  }

  // TypeScript complains when it is one function calls and the event name is factored out
  if (initialValues.value === undefined) {
    emit('completed', {
      mission: mission.value,
      title: title.value,
      subtitle: subtitle.value,
      chapters: chapters.value,
      missionQuestions: missionQuestions.value,
    });
  } else {
    emit('updated', initialValues.value?.id, {
      ...initialValues.value,
      id: initialValues.value?.id,
      mission: mission.value,
      title: title.value,
      subtitle: subtitle.value,
      chapters: chapters.value,
      missionQuestions: missionQuestions.value,
    });

    await loggingService.logAction(
      PAGES.BOOKMISSIONSTATEMENT,
      ACTIONS.UPDATE,
      `Book is updated from the mission statement: ${
        title.value || 'Untitled book'
      }`,
      MODULE_NAME,
      initialValues.value?.id as string,
    );

    if (autosave.value === true && book.value?.id) {
      await router.push(`/books/${book.value.id}`);
    }
  }
  if (onBoardingActive.value === true) {
    onBoardingActive.value = false;
  }
}

const book = computed(() => {
  const updatedBook = {
    ...initialValues.value,
    id: initialValues.value?.id,
    mission: mission.value,
    title: title.value,
    subtitle: subtitle.value,
    chapters: chapters.value,
    missionQuestions: missionQuestions.value,
  };
  return updatedBook;
});
// handle updates on book object
watchDebounced(
  book,
  (newBook) => {
    if (autosave.value === true) {
      setBook(book.value.id, newBook);
    }
  },
  { debounce: 500 },
);
// handle updates on book chapters
watchDebounced(
  chapters,
  (newChapters) => {
    if (autosave.value === true) {
      book.value.chapters = newChapters;
      updateBook(book.value.id, book.value);
    }
  },
  { debounce: 500, deep: true },
);
</script>
