<template>
  <OnboardingStepWrapper
    theme="faith-story"
    content-title="Write your Faith Story Book"
    content-subtitle="Share your spiritual journey and inspire others through your faith experiences."
    content-icon="auto_stories"
    :show-actions="false"
  >
    <OnboardingFormSection
      label="Target Audience"
      description="Define who you want to reach with your faith story."
      icon="people"
    >
      <div class="form-grid grid-2">
        <OnboardingInput
          v-model="gender"
          label="Target audience gender"
          type="select"
          :options="['Male', 'Female', 'Both']"
          prepend-icon="person"
        />

        <OnboardingInput
          v-model="age"
          label="Target audience age"
          type="select"
          :options="ageOptions"
          multiple
          use-chips
          prepend-icon="cake"
        />
      </div>

      <OnboardingInput
        v-model="mainReason"
        label="What is the main reason you want to share your faith story?"
        type="textarea"
        :rows="3"
        placeholder="Examples: Inspire others, share a testimony, help people going through similar struggles"
        append-icon="mic"
        @append-click="openTranscription('mainReason')"
      />
    </OnboardingFormSection>

    <OnboardingFormSection
      label="Story Type & Structure"
      description="Help us understand the nature and structure of your faith story."
      icon="category"
      class="q-gutter-sm"
    >
      <OnboardingOptionGrid
        :options="bestDescriptionOptions"
        v-model:selected-value="bestDescription"
        :columns="3"
      />

      <OnboardingInput
        v-if="bestDescription === 'Other'"
        v-model="otherBestDescription"
        label="If other, please specify"
        type="textarea"
        :rows="2"
        placeholder="Describe your unique faith story type..."
        append-icon="mic"
        @append-click="openTranscription('bestDescription')"
      />

      <OnboardingOptionGrid
        :options="bookOrderOptions"
        v-model:selected-value="bookOrder"
        :columns="2"
      />
    </OnboardingFormSection>

    <OnboardingFormSection
      label="Faith Background & Audience"
      description="Provide details about your spiritual background and target readers."
      icon="church"
      class="q-gutter-sm"
    >
      <OnboardingInput
        v-model="bookFocused"
        label="Is your story focused on a specific faith or spiritual background?"
        type="textarea"
        :rows="2"
        placeholder="Examples: Christian, Muslim, New Age, Spiritual but not religious"
        append-icon="mic"
        @append-click="openTranscription('bookFocused')"
      />

      <OnboardingInput
        v-model="description"
        label="Describe your target audience in a few words"
        type="textarea"
        :rows="2"
        placeholder="Examples: Women struggling with doubt, men leaving prison, teens in crisis, fellow believers"
        append-icon="mic"
        @append-click="openTranscription('description')"
      />
    </OnboardingFormSection>

    <OnboardingFormSection
      label="Key Events & Impact"
      description="Define the core moments and desired impact of your faith story."
      icon="star"
      class="q-gutter-sm"
    >
      <OnboardingInput
        v-model="keyLifeEvents"
        label="What are 3-5 key life events or spiritual moments you want to include?"
        type="textarea"
        :rows="3"
        placeholder="List the pivotal moments in your faith journey..."
        :loading="loadingKeyLifeEvents"
        :disabled="loadingKeyLifeEvents"
      >
        <template #append>
          <q-btn
            flat
            icon="mic"
            @click="openTranscription('keyLifeEvents')"
            :disable="loadingKeyLifeEvents"
          >
            <q-tooltip>Transcribe</q-tooltip>
          </q-btn>
          <MannyButton
            variant="round"
            :loading="loadingKeyLifeEvents"
            :disabled="loadingKeyLifeEvents || !canGenerateKeyLifeEvents"
            tooltip="Ask Manny to suggest key life events"
            @click="generateKeyLifeEvents(false)"
          />
        </template>
      </OnboardingInput>

      <OnboardingInput
        v-model="readersTakeaway"
        label="What do you hope readers will take away from your story?"
        type="textarea"
        :rows="3"
        placeholder="Examples: Hope, belief in miracles, deeper faith, encouragement"
        :loading="loadingReadersTakeaway"
        :disabled="loadingReadersTakeaway"
      >
        <template #append>
          <q-btn
            flat
            icon="mic"
            @click="openTranscription('readersTakeaway')"
            :disable="loadingReadersTakeaway"
          >
            <q-tooltip>Transcribe</q-tooltip>
          </q-btn>
          <MannyButton
            variant="round"
            :loading="loadingReadersTakeaway"
            :disabled="loadingReadersTakeaway || !canGenerateReadersTakeaway"
            tooltip="Ask Manny to suggest reader takeaways"
            @click="generateReadersTakeaway(false)"
          />
        </template>
      </OnboardingInput>

      <OnboardingInput
        v-model="authorImpact"
        label="What are 2-3 things you hope this book does for you?"
        type="textarea"
        :rows="2"
        placeholder="Examples: Healing, leaving a legacy, grow a platform, lead others to God"
        :loading="loadingAuthorImpact"
        :disabled="loadingAuthorImpact"
      >
        <template #append>
          <q-btn
            flat
            icon="mic"
            @click="openTranscription('authorImpact')"
            :disable="loadingAuthorImpact"
          >
            <q-tooltip>Transcribe</q-tooltip>
          </q-btn>
          <MannyButton
            variant="round"
            :loading="loadingAuthorImpact"
            :disabled="loadingAuthorImpact || !canGenerateAuthorImpact"
            tooltip="Ask Manny to suggest author benefits"
            @click="generateAuthorImpact(false)"
          />
        </template>
      </OnboardingInput>
    </OnboardingFormSection>
  </OnboardingStepWrapper>

  <q-dialog v-model="transcriptionDialog">
    <BookTranscriptionApp
      persistent
      @completed="completedTranscript"
      @close="transcriptionDialog = false"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useVModels, toRefs } from '@vueuse/core';
import { useQuasar } from 'quasar';
import { composeText } from 'src/shared/api/openai';
import { confirmOverrideText, warn } from 'src/shared/lib/quasar-dialogs';
import BookTranscriptionApp from '../../BookTranscriptionApp.vue';
import {
  OnboardingStepWrapper,
  OnboardingFormSection,
  OnboardingInput,
  OnboardingOptionGrid,
  MannyButton,
  type OnboardingOption,
} from '../shared';

const transcriptionDialog = ref(false);
const focusedField = ref('');

export interface Questions {
  gender: 'Males' | 'Females' | 'Both';
  age: string[];
  keyLifeEvents: string;
  readersTakeaway: string;
  mainReason: string;
  bestDescription: string;
  otherBestDescription: string;
  description: string;
  bookOrder: string;
  bookFocused: string;
  authorImpact: string;
}

const props = withDefaults(
  defineProps<{
    questions: Questions;
    updating?: boolean;
    isScreenBiggerMd?: boolean;
  }>(),
  {
    updating: false,
  },
);
const emit = defineEmits<{
  'update:questions': [value: Questions];
}>();
const { questions } = useVModels(props, emit);
const {
  gender,
  age,
  description,
  keyLifeEvents,
  readersTakeaway,
  authorImpact,
  bestDescription,
  otherBestDescription,
  mainReason,
  bookOrder,
  bookFocused,
} = toRefs(questions);

const $q = useQuasar();

const ageConcat = new Intl.ListFormat('en', {
  style: 'long',
  type: 'conjunction',
});

enum Modes {
  Manual,
  Help,
}
const selectedField = ref('');
const mode = ref(Modes.Manual);
const completedTranscript = (data: any) => {
  const content = data.content;
  switch (selectedField.value) {
    case 'keyLifeEvents':
      keyLifeEvents.value = content;
      break;
    case 'description':
      description.value = content;
      break;
    case 'mainReason':
      mainReason.value = content;
      break;
  }
  transcriptionDialog.value = false;
};
const ageOptions = [
  'Any Age',
  'Under 18',
  '18-25',
  '25-35',
  '35-50',
  '50-65',
  '65+',
];
const bestDescriptionOptions: OnboardingOption[] = [
  {
    label: 'Personal Testimony',
    value: 'A personal testimony of coming to faith',
    description: 'Your journey to discovering faith',
    icon: 'person',
  },
  {
    label: 'Transformation Story',
    value: 'A story of transformation through faith',
    description: 'How faith changed your life',
    icon: 'transform',
  },
  {
    label: 'Life Lessons',
    value: 'A series of faith-based life lessons',
    description: 'Wisdom gained through faith experiences',
    icon: 'school',
  },
  {
    label: 'Overcoming Adversity',
    value: 'A story of overcoming adversity with faith',
    description: 'Triumph through spiritual strength',
    icon: 'trending_up',
  },
  {
    label: 'Other',
    value: 'Other',
    description: 'A unique faith story type',
    icon: 'more_horiz',
  },
];

const bookOrderOptions: OnboardingOption[] = [
  {
    label: 'Chronological',
    value: 'Chronological',
    description: 'Tell your story in order of life events',
    icon: 'timeline',
  },
  {
    label: 'Thematic',
    value: 'Thematic',
    description: 'Organize by lessons, challenges, and turning points',
    icon: 'category',
  },
];

// Computed properties for enabling generation buttons
const canGenerateKeyLifeEvents = computed(() => {
  return gender.value && age.value.length > 0 && mainReason.value;
});

const canGenerateReadersTakeaway = computed(() => {
  return canGenerateKeyLifeEvents.value && keyLifeEvents.value;
});

const canGenerateAuthorImpact = computed(() => {
  return canGenerateReadersTakeaway.value && readersTakeaway.value;
});

// Helper function for transcription
const openTranscription = (field: string) => {
  selectedField.value = field;
  transcriptionDialog.value = true;
};

const loadingKeyLifeEvents = ref(false);
async function generateKeyLifeEvents(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !mainReason.value ||
      !bestDescription.value ||
      (bestDescription.value === 'Other' && !otherBestDescription.value)) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.keyLifeEvents;
  if (keyLifeEvents.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.keyLifeEvents =
      typeof results.value.keyLifeEvents === 'string'
        ? results.value.keyLifeEvents
        : await results.value.keyLifeEvents;
  }

  if (!invoking) loadingKeyLifeEvents.value = true;
  if (results.value.keyLifeEvents) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyLifeEvents}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) keyLifeEvents.value = response;
    // save the results
    results.value.keyLifeEvents = `${results.value.keyLifeEvents} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingKeyLifeEvents.value = false;
  }
}

const loadingAuthorImpact = ref(false);
async function generateAuthorImpact(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !mainReason.value ||
      !keyLifeEvents.value ||
      !readersTakeaway.value ||
      (bestDescription.value === 'Other' && !otherBestDescription.value)) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.authorImpact;
  if (authorImpact.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.authorImpact =
      typeof results.value.authorImpact === 'string'
        ? results.value.authorImpact
        : await results.value.authorImpact;
  }

  if (!invoking) loadingAuthorImpact.value = true;
  if (results.value.authorImpact) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyLifeEvents}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) authorImpact.value = response;
    // save the results
    results.value.authorImpact = `${results.value.authorImpact} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingAuthorImpact.value = false;
  }
}

const loadingReadersTakeaway = ref(false);
async function generateReadersTakeaway(invoking: boolean = false) {
  if (
    (!gender.value ||
      age.value.length === 0 ||
      !description.value ||
      !mainReason.value ||
      !keyLifeEvents.value ||
      (bestDescription.value === 'Other' && !otherBestDescription.value)) &&
    !invoking
  ) {
    warn($q, {
      title: 'Missing fields',
      message: 'Please fill in all the previous fields first.',
    });
    return;
  }
  let promptRequest: string = updatedPrompt.value.readersTakeaway;
  if (readersTakeaway.value && !invoking) {
    const shouldDelete = await confirmOverrideText($q);

    if (!shouldDelete) {
      return;
    }
    results.value.readersTakeaway =
      typeof results.value.readersTakeaway === 'string'
        ? results.value.readersTakeaway
        : await results.value.readersTakeaway;
  }

  if (!invoking) loadingReadersTakeaway.value = true;
  if (results.value.readersTakeaway) {
    promptRequest = `${promptRequest} Please refrain from utilizing the following results or repeating the same sentences from the following results. ${results.value.keyLifeEvents}`;
  }
  try {
    const response = await composeText(promptRequest);
    if (!invoking) readersTakeaway.value = response;
    // save the results
    results.value.readersTakeaway = `${results.value.readersTakeaway} ${response}`;
  } catch (e) {
    console.error(e);
    alert('An error occurred: ' + e);
  } finally {
    loadingReadersTakeaway.value = false;
  }
}

const ageGroups = computed(() => ageConcat.format(age.value));

// Updating of Open AI Prompt
const results = ref({} as { [key: string]: any });

const promptKeyLifeEvents = computed(
  () => `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br/>
  Using the details above, identify 3–5 powerful life events or spiritual moments that the author might include in their faith-based story. Follow these guidelines:
  <br/>
  <ul>
  <li>Each response should be emotionally resonant or spiritually significant</li>
  <li>Use plain language and keep each response under 12 words</li>
  <li>Do not separate by age band or use bullet points</li>
  <li>provide each response in a new line, without a number, bullet point, or break tag</li>
  </ul>`,
);

const promptAuthorImpact = computed(
  () => `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Desired Reader Takeaway: ${questions?.value?.readersTakeaway}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br/>
  Using the details above, identify 2–3 personal or professional goals the author may hope to achieve by writing this faith-based book. Follow these guidelines: <br/>
  <ul>
  <li>Keep each goal under 12 words</li>
  <li>Focus on emotional healing, spiritual impact, or personal purpose</li>
  <li>Include practical goals only if they’re meaningful to the author’s faith journey</li>
  <li>List each response on a new line without numbers, bullets, or breaks</li>
  </ul>`,
);

const promptReadersTakeaway = computed(
  () => `
  <p>Main Reason for Sharing Story: ${questions?.value?.mainReason}</p>
  <p>Type of Faith Story: ${
    questions?.value?.otherBestDescription || questions?.value?.bestDescription
  }</p>
  <p>Story Structure Preference: ${questions?.value?.bookOrder}</p>
  <p>Spiritual Background: ${questions?.value?.bookFocused}</p>
  <p>Target Audience Description: ${questions?.value?.description}</p>
  <p>Key Life Events or Spiritual Moments: ${questions?.value
    ?.keyLifeEvents}</p>
  <p>Gender: ${questions?.value?.gender}</p>
  <p>Age: ${questions?.value?.age}</p>

  <br/>
  Using the details above, identify 3–4 key takeaways the reader should gain from this faith-based story. Follow these guidelines:
   <br/>
  <ul>
  <li>Use short, emotionally meaningful sentences under 12 words</li>
  <li>Focus on spiritual, emotional, or personal growth themes</li>
  <li>Align takeaways with the author’s faith background and target audience</li>
  <li>List each response on a new line without numbers, bullets, or breaks</li>
  </ul>`,
);

onMounted(async () => {
  if (age.value.length) {
    mode.value = Modes.Help;
  }
  if (keyLifeEvents.value) {
    try {
      await generateKeyLifeEvents(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (authorImpact.value) {
    try {
      await generateAuthorImpact(true);
    } catch (e) {
      console.error(e);
    }
  }
  if (readersTakeaway.value) {
    try {
      await generateReadersTakeaway(true);
    } catch (e) {
      console.error(e);
    }
  }
});

const updatedPrompt = computed(() => ({
  keyLifeEvents: promptKeyLifeEvents.value,
  authorImpact: promptAuthorImpact.value,
  readersTakeaway: promptReadersTakeaway.value,
}));
</script>

<style scoped>
:deep(.q-field__prepend) {
  margin-left: -4px;
  margin-right: -4px;
}
</style>
